<template>
  <div class="relative h-full w-full flex-1" ref="container">
    <!-- 养鱼池信息弹窗 -->
    <div
      v-if="showPoolInfo"
      class="pool-info-popup"
      :class="{
        'alarm-popup': selectedPoolInfo.isAlarm,
        'popup-below': popupPosition.isBelow,
      }"
      :style="{
        left: popupPosition.x + 'px',
        top: popupPosition.y + 'px',
        '--arrow-left': popupPosition.arrowLeft + 'px',
      }"
    >
      <!-- 指向池子的小三角 -->
      <div
        class="popup-arrow"
        :class="{ 'alarm-arrow': selectedPoolInfo.isAlarm }"
      ></div>
      <div class="popup-header">
        <h3 class="popup-title">
          {{ selectedPoolInfo.name }}
          <span v-if="selectedPoolInfo.isAlarm" class="alarm-badge">报警</span>
        </h3>
        <button class="close-btn" @click="closePopup">×</button>
      </div>
      <div class="popup-content">
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">PH值</div>
            <div class="info-value">{{ selectedPoolInfo.ph }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">水温</div>
            <div class="info-value">{{ selectedPoolInfo.temperature }}°C</div>
          </div>
          <div class="info-item">
            <div class="info-label">溶解氧</div>
            <div class="info-value">{{ selectedPoolInfo.oxygen }} mg/L</div>
          </div>
          <div class="info-item">
            <div class="info-label">水位</div>
            <div class="info-value">{{ selectedPoolInfo.waterLevel }} m</div>
          </div>
          <div class="info-item">
            <div class="info-label">盐度</div>
            <div class="info-value">{{ selectedPoolInfo.salinity }}%</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import * as THREE from "three";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js";
// 导入lil.gui
import { GUI } from "three/examples/jsm/libs/lil-gui.module.min.js";

// 获取容器引用
const container = ref<HTMLDivElement>();

// 弹窗相关响应式数据
const showPoolInfo = ref(false);
const popupPosition = ref({ x: 0, y: 0, arrowLeft: 0, isBelow: false });
const selectedPoolInfo = ref({
  name: "",
  ph: "",
  temperature: "",
  oxygen: "",
  waterLevel: "",
  salinity: "",
  isAlarm: false,
});

// 轮播相关变量
let autoRotationTimer: number | null = null;
let currentRotationIndex = 0;
const isAutoRotating = ref(false);

// 养鱼池数据
let poolsData: any[] = [];

// 正常范围定义
const normalRanges = {
  water_temperature: { min: 20, max: 29 },
  PH: { min: 7.3, max: 8.8 },
  salinity: { min: 10, max: 26 },
  water_level: { min: 0.7, max: 1.45 },
  dissolved_oxygen: { min: 5, max: Infinity },
};

// Three.js 相关变量
let scene: THREE.Scene;
let camera: THREE.PerspectiveCamera;
let renderer: THREE.WebGLRenderer;
let controls: OrbitControls;
let poolModel: THREE.Group | null = null;
let animationId: number;
let raycaster: THREE.Raycaster;
let mouse: THREE.Vector2;
let poolObjects: THREE.Object3D[] = [];
let selectedPool: THREE.Object3D | null = null;
let originalMaterials: Map<THREE.Object3D, THREE.Material | THREE.Material[]> =
  new Map();

// GUI 相关变量
let gui: GUI | null = null;
let ambientLight: THREE.AmbientLight;
let directionalLight: THREE.DirectionalLight;
let gridHelper: THREE.GridHelper;
let axesHelper: THREE.AxesHelper;

// 手动选择相关变量
let manualSelectionMode = false;
let manualSelectedObjects: Set<THREE.Mesh> = new Set();

// GUI 控制参数
const guiParams = {
  // 相机控制
  camera: {
    x: 0,
    y: 5,
    z: 10,
    fov: 45,
    resetCamera: () => resetCamera(),
  },
  // 光照控制
  lighting: {
    ambientIntensity: 0.6,
    ambientColor: "#404040",
    directionalIntensity: 0.8,
    directionalColor: "#ffffff",
    directionalX: 10,
    directionalY: 10,
    directionalZ: 5,
  },
  // 模型控制
  model: {
    scale: 0.07,
    x: 0,
    y: 0,
    z: 6.5,
    rotationX: 0.18,
    rotationY: 0,
    rotationZ: 0,
  },
  // 轮播控制
  rotation: {
    enabled: false,
    speed: 3000,
    start: () => startAutoRotation(),
    stop: () => stopAutoRotation(),
  },
  // 显示控制
  display: {
    showGrid: true,
    showAxes: true,
    wireframe: false,
  },
};

// 加载养鱼池数据
async function loadPoolsData() {
  try {
    const response = await fetch("/src/components/data.json");
    poolsData = await response.json();
    console.log("养鱼池数据加载成功:", poolsData.length, "个池子");
  } catch (error) {
    console.error("加载养鱼池数据失败:", error);
    // 如果加载失败，使用默认数据
    poolsData = [];
  }
}

// 重置相机位置
function resetCamera() {
  if (!camera) return;

  camera.position.set(
    guiParams.camera.x,
    guiParams.camera.y,
    guiParams.camera.z
  );
  camera.fov = guiParams.camera.fov;
  camera.updateProjectionMatrix();
  camera.lookAt(0, 0, 0);

  if (controls) {
    controls.reset();
  }
}

// 初始化GUI
function initGUI() {
  // 如果GUI已经存在，先销毁它
  if (gui) {
    gui.destroy();
  }

  // 创建GUI
  gui = new GUI();
  gui.title("养鱼池控制面板");

  // 相机控制文件夹
  const cameraFolder = gui.addFolder("相机控制");
  cameraFolder
    .add(guiParams.camera, "x", -20, 20, 0.1)
    .name("X位置")
    .onChange(() => updateCamera());
  cameraFolder
    .add(guiParams.camera, "y", 0, 20, 0.1)
    .name("Y位置")
    .onChange(() => updateCamera());
  cameraFolder
    .add(guiParams.camera, "z", -20, 20, 0.1)
    .name("Z位置")
    .onChange(() => updateCamera());
  cameraFolder
    .add(guiParams.camera, "fov", 10, 120, 1)
    .name("视角")
    .onChange(() => updateCamera());
  cameraFolder.add(guiParams.camera, "resetCamera").name("重置相机");

  // 光照控制文件夹
  const lightingFolder = gui.addFolder("光照控制");
  lightingFolder
    .add(guiParams.lighting, "ambientIntensity", 0, 2, 0.1)
    .name("环境光强度")
    .onChange(() => updateLighting());
  lightingFolder
    .addColor(guiParams.lighting, "ambientColor")
    .name("环境光颜色")
    .onChange(() => updateLighting());
  lightingFolder
    .add(guiParams.lighting, "directionalIntensity", 0, 2, 0.1)
    .name("方向光强度")
    .onChange(() => updateLighting());
  lightingFolder
    .addColor(guiParams.lighting, "directionalColor")
    .name("方向光颜色")
    .onChange(() => updateLighting());
  lightingFolder
    .add(guiParams.lighting, "directionalX", -20, 20, 0.5)
    .name("方向光X")
    .onChange(() => updateLighting());
  lightingFolder
    .add(guiParams.lighting, "directionalY", -20, 20, 0.5)
    .name("方向光Y")
    .onChange(() => updateLighting());
  lightingFolder
    .add(guiParams.lighting, "directionalZ", -20, 20, 0.5)
    .name("方向光Z")
    .onChange(() => updateLighting());

  // 模型控制文件夹
  const modelFolder = gui.addFolder("模型控制");
  modelFolder
    .add(guiParams.model, "scale", 0.01, 0.2, 0.001)
    .name("缩放")
    .onChange(() => updateModel());
  modelFolder
    .add(guiParams.model, "x", -10, 10, 0.1)
    .name("X位置")
    .onChange(() => updateModel());
  modelFolder
    .add(guiParams.model, "y", -5, 5, 0.1)
    .name("Y位置")
    .onChange(() => updateModel());
  modelFolder
    .add(guiParams.model, "z", -10, 20, 0.1)
    .name("Z位置")
    .onChange(() => updateModel());
  modelFolder
    .add(guiParams.model, "rotationX", -Math.PI, Math.PI, 0.01)
    .name("X轴旋转")
    .onChange(() => updateModel());
  modelFolder
    .add(guiParams.model, "rotationY", -Math.PI, Math.PI, 0.01)
    .name("Y轴旋转")
    .onChange(() => updateModel());
  modelFolder
    .add(guiParams.model, "rotationZ", -Math.PI, Math.PI, 0.01)
    .name("Z轴旋转")
    .onChange(() => updateModel());

  // 轮播控制文件夹
  const rotationFolder = gui.addFolder("轮播控制");
  rotationFolder
    .add(guiParams.rotation, "enabled")
    .name("启用轮播")
    .listen()
    .onChange((value: boolean) => {
      if (value) {
        startAutoRotation();
      } else {
        stopAutoRotation();
      }
    });
  rotationFolder
    .add(guiParams.rotation, "speed", 1000, 10000, 100)
    .name("轮播速度(ms)");
  rotationFolder.add(guiParams.rotation, "start").name("开始轮播");
  rotationFolder.add(guiParams.rotation, "stop").name("停止轮播");

  // 显示控制文件夹
  const displayFolder = gui.addFolder("显示控制");
  displayFolder
    .add(guiParams.display, "showGrid")
    .name("显示网格")
    .onChange(() => updateDisplay());
  displayFolder
    .add(guiParams.display, "showAxes")
    .name("显示坐标轴")
    .onChange(() => updateDisplay());
  displayFolder
    .add(guiParams.display, "wireframe")
    .name("线框模式")
    .onChange(() => updateDisplay());

  // 颜色控制
  const colorFolder = gui.addFolder("颜色控制");
  let colorObj = {
    setBlueColor: function () {
      setPoolsBlueColor();
    },
    resetColors: function () {
      if (poolModel) {
        resetAllColors(poolModel);
      }
    },
    debugObjects: function () {
      if (poolModel) {
        debugModelObjects(poolModel);
      }
    },
  };
  colorFolder.add(colorObj, "setBlueColor").name("设置池子为蓝色");
  colorFolder.add(colorObj, "resetColors").name("恢复原始颜色");
  colorFolder.add(colorObj, "debugObjects").name("调试对象名称");

  // 添加手动选择模式
  const manualFolder = colorFolder.addFolder("手动选择模式");
  let manualObj = {
    enableSelection: false,
    selectedCount: 0,
    clearSelection: function () {
      clearManualSelection();
    },
  };
  manualFolder
    .add(manualObj, "enableSelection")
    .name("启用点击选择")
    .onChange((value: boolean) => {
      manualSelectionMode = value;
      if (value) {
        console.log("点击选择模式已启用，点击网格来选择要变蓝的对象");
      } else {
        console.log("点击选择模式已禁用");
      }
    });
  manualFolder.add(manualObj, "selectedCount").name("已选择数量").listen();
  manualFolder.add(manualObj, "clearSelection").name("清除选择");

  // 添加应用蓝色按钮
  let applyObj = {
    applyBlue: function () {
      setManualSelectedBlue();
    },
  };
  manualFolder.add(applyObj, "applyBlue").name("应用蓝色到选中对象");

  // 全屏控制
  let eventObj = {
    Fullscreen: function () {
      document.body.requestFullscreen();
      console.log("全屏");
    },
    ExitFullscreen: function () {
      document.exitFullscreen();
      console.log("退出全屏");
    },
  };
  gui.add(eventObj, "Fullscreen").name("全屏");
  gui.add(eventObj, "ExitFullscreen").name("退出全屏");

  console.log("GUI初始化完成");
}

// 更新相机
function updateCamera() {
  if (!camera) return;

  camera.position.set(
    guiParams.camera.x,
    guiParams.camera.y,
    guiParams.camera.z
  );
  camera.fov = guiParams.camera.fov;
  camera.updateProjectionMatrix();
}

// 更新光照
function updateLighting() {
  if (ambientLight) {
    ambientLight.intensity = guiParams.lighting.ambientIntensity;
    ambientLight.color.setHex(
      parseInt(guiParams.lighting.ambientColor.replace("#", "0x"))
    );
  }

  if (directionalLight) {
    directionalLight.intensity = guiParams.lighting.directionalIntensity;
    directionalLight.color.setHex(
      parseInt(guiParams.lighting.directionalColor.replace("#", "0x"))
    );
    directionalLight.position.set(
      guiParams.lighting.directionalX,
      guiParams.lighting.directionalY,
      guiParams.lighting.directionalZ
    );
  }
}

// 更新模型
function updateModel() {
  if (!poolModel) return;

  poolModel.scale.setScalar(guiParams.model.scale);
  poolModel.position.set(
    guiParams.model.x,
    guiParams.model.y,
    guiParams.model.z
  );
  poolModel.rotation.x = guiParams.model.rotationX;
  poolModel.rotation.y = guiParams.model.rotationY;
  poolModel.rotation.z = guiParams.model.rotationZ;
}

// 更新显示
function updateDisplay() {
  // if (gridHelper) {
  //   gridHelper.visible = guiParams.display.showGrid;
  // }

  // if (axesHelper) {
  //   axesHelper.visible = guiParams.display.showAxes;
  // }

  if (poolModel) {
    poolModel.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        if (Array.isArray(child.material)) {
          child.material.forEach((material) => {
            material.wireframe = guiParams.display.wireframe;
          });
        } else {
          child.material.wireframe = guiParams.display.wireframe;
        }
      }
    });
  }
}

// 检查数值是否在正常范围内
function isValueNormal(key: string, value: number): boolean {
  const range = normalRanges[key as keyof typeof normalRanges];
  if (!range) return true;

  if (range.max === Infinity) {
    return value >= range.min;
  }
  return value >= range.min && value <= range.max;
}

// 检查池子是否有报警
function checkPoolAlarm(poolData: any): boolean {
  return (
    !isValueNormal("water_temperature", poolData.water_temperature) ||
    !isValueNormal("PH", poolData.PH) ||
    !isValueNormal("salinity", poolData.salinity) ||
    !isValueNormal("water_level", poolData.water_level) ||
    !isValueNormal("dissolved_oxygen", poolData.dissolved_oxygen)
  );
}

// 从池子对象中获取编号
function getPoolNumber(poolObject: THREE.Object3D): number {
  // 优先使用我们分配的编号
  if (poolObject.userData && poolObject.userData.poolNumber) {
    return poolObject.userData.poolNumber;
  }

  // 如果没有分配编号，尝试从名称中提取
  const match = poolObject.name.match(/\d+/);
  if (match) {
    return parseInt(match[0]);
  }

  // 如果都没有找到，返回1作为默认值
  return 1;
}

// 获取池子数据
function getPoolData(poolIndex: number) {
  // 确保索引在有效范围内，如果超出范围则取前36个
  const dataIndex = Math.min(poolIndex - 1, 35);
  const data = poolsData[dataIndex] || {
    id: poolIndex,
    water_temperature: 25,
    PH: 7.5,
    dissolved_oxygen: 6,
    water_level: 1.0,
    salinity: 15,
  };

  const isAlarm = checkPoolAlarm(data);

  return {
    name: `养鱼池${poolIndex.toString().padStart(2, "0")}`,
    ph: data.PH.toFixed(1),
    temperature: data.water_temperature.toFixed(1),
    oxygen: data.dissolved_oxygen.toFixed(1),
    waterLevel: data.water_level.toFixed(2),
    salinity: data.salinity.toString(),
    isAlarm,
  };
}

// 初始化 Three.js
function initThreeJS() {
  if (!container.value) return;

  // 获取容器尺寸
  const width = container.value.clientWidth;
  const height = container.value.clientHeight;

  // 创建场景
  scene = new THREE.Scene();

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    45, // 视角
    width / height, // 宽高比
    0.1, // 近平面
    1000 // 远平面
  );

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({
    antialias: true,
    alpha: true, // 启用透明度支持
  });
  renderer.setSize(width, height);
  renderer.setPixelRatio(window.devicePixelRatio);

  // 设置透明背景
  renderer.setClearColor(0x000000, 0); // 黑色背景，透明度为0

  // 将渲染器添加到容器中
  container.value.appendChild(renderer.domElement);

  // 初始化射线投射器和鼠标向量
  raycaster = new THREE.Raycaster();
  mouse = new THREE.Vector2();

  // 创建轨道控制器
  controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true; // 启用阻尼
  controls.dampingFactor = 0.05; // 阻尼系数
  controls.screenSpacePanning = false; // 禁用屏幕空间平移
  controls.minDistance = 1; // 最小距离
  controls.maxDistance = 50; // 最大距离
  controls.maxPolarAngle = Math.PI / 2; // 最大极角（防止相机翻转到地面以下）

  // 添加鼠标点击事件监听器
  renderer.domElement.addEventListener("click", onMouseClick, false);

  // 添加世界坐标辅助器（显示XYZ轴）
  // axesHelper = new THREE.AxesHelper(5);
  // scene.add(axesHelper);

  // 添加网格辅助器
  // gridHelper = new THREE.GridHelper(20, 20);
  // scene.add(gridHelper);

  // 添加光源
  ambientLight = new THREE.AmbientLight(
    parseInt(guiParams.lighting.ambientColor.replace("#", "0x")),
    guiParams.lighting.ambientIntensity
  );
  scene.add(ambientLight);

  directionalLight = new THREE.DirectionalLight(
    parseInt(guiParams.lighting.directionalColor.replace("#", "0x")),
    guiParams.lighting.directionalIntensity
  );
  directionalLight.position.set(
    guiParams.lighting.directionalX,
    guiParams.lighting.directionalY,
    guiParams.lighting.directionalZ
  );
  scene.add(directionalLight);

  // 加载FBX模型
  loadPoolModel();

  // 设置相机位置
  camera.position.set(
    guiParams.camera.x,
    guiParams.camera.y,
    guiParams.camera.z
  );
  camera.fov = guiParams.camera.fov;
  camera.updateProjectionMatrix();
  camera.lookAt(0, 0, 0);

  // 初始化GUI
  // initGUI();

  // 开始动画
  animate();
}

// 鼠标点击事件处理
function onMouseClick(event: MouseEvent) {
  // 计算鼠标位置（标准化设备坐标）
  const rect = renderer.domElement.getBoundingClientRect();
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

  // 更新射线投射器
  raycaster.setFromCamera(mouse, camera);

  // 如果是手动选择模式，检测所有对象
  if (manualSelectionMode && poolModel) {
    const allIntersects = raycaster.intersectObject(poolModel, true);

    if (allIntersects.length > 0) {
      const clickedMesh = allIntersects[0].object;

      if (clickedMesh instanceof THREE.Mesh) {
        // 切换选择状态
        if (manualSelectedObjects.has(clickedMesh)) {
          // 取消选择
          manualSelectedObjects.delete(clickedMesh);
          if (originalMaterials.has(clickedMesh)) {
            clickedMesh.material = originalMaterials.get(
              clickedMesh
            ) as THREE.Material;
          }
          console.log(`取消选择: ${clickedMesh.name || "未命名网格"}`);
        } else {
          // 添加选择
          manualSelectedObjects.add(clickedMesh);
          // 临时高亮显示（绿色表示已选择）
          if (!originalMaterials.has(clickedMesh)) {
            originalMaterials.set(clickedMesh, clickedMesh.material);
          }
          const highlightMaterial = new THREE.MeshBasicMaterial({
            color: 0x00ff00,
            transparent: true,
            opacity: 0.7,
          });
          clickedMesh.material = highlightMaterial;
          console.log(`选择: ${clickedMesh.name || "未命名网格"}`);
        }

        // 更新GUI中的计数
        if (gui) {
          const controllers = gui.controllersRecursive();
          const countController = controllers.find(
            (c) => c.property === "selectedCount"
          );
          if (countController) {
            (countController.object as any).selectedCount =
              manualSelectedObjects.size;
          }
        }
      }
    }
    return; // 在手动选择模式下，不执行正常的池子选择逻辑
  }

  // 正常的池子选择逻辑
  const intersects = raycaster.intersectObjects(poolObjects, true);

  if (intersects.length > 0) {
    const clickedObject = intersects[0].object;

    // 找到包含"养鱼池"名称的父对象
    let poolObject = clickedObject;
    while (poolObject && !poolObject.name.includes("养鱼池")) {
      poolObject = poolObject.parent as THREE.Object3D;
    }

    if (poolObject && poolObject.name.includes("养鱼池")) {
      // 手动点击时暂停轮播
      restartAutoRotation();
      // 传递点击位置信息
      selectPool(poolObject, intersects[0].point);
    }
  }
}

// 选择并高亮养鱼池
function selectPool(pool: THREE.Object3D, clickPoint?: THREE.Vector3) {
  // 取消之前的选择
  if (selectedPool) {
    resetPoolHighlight(selectedPool);
  }

  // 设置新的选择
  selectedPool = pool;
  highlightPool(pool);

  // 显示弹窗
  showPoolPopup(pool, clickPoint);

  console.log(`选中养鱼池: ${pool.name}`, {
    position: pool.position,
    userData: pool.userData,
  });
}

// 显示养鱼池信息弹窗
function showPoolPopup(pool: THREE.Object3D, clickPoint?: THREE.Vector3) {
  // 从池子对象中获取编号
  const poolNumber = getPoolNumber(pool);
  const poolData = getPoolData(poolNumber);

  selectedPoolInfo.value = {
    name: poolData.name,
    ph: poolData.ph,
    temperature: poolData.temperature,
    oxygen: poolData.oxygen,
    waterLevel: poolData.waterLevel,
    salinity: poolData.salinity,
    isAlarm: poolData.isAlarm,
  };

  // 计算弹窗位置（在池子上方）
  if (container.value) {
    let screenPosition;

    if (clickPoint) {
      // 使用点击点的位置
      screenPosition = worldToScreen(clickPoint);
    } else {
      // 使用池子中心位置
      const poolWorldPosition = new THREE.Vector3();
      pool.getWorldPosition(poolWorldPosition);
      screenPosition = worldToScreen(poolWorldPosition);
    }

    // 计算弹窗位置和箭头位置
    const popupWidth = 180;
    const popupHeight = 140;
    const arrowSize = 8;
    const margin = 20; // 弹窗与池子之间的间距

    // 池子在屏幕上的位置（箭头要指向这里）
    // const poolScreenX = screenPosition.x;
    const poolScreenY = screenPosition.y;

    // 弹窗默认放在池子上方，居中对齐
    // let popupX = poolScreenX - popupWidth / 2;
    let popupY = poolScreenY - popupHeight - arrowSize - margin;
    let isBelow = false;

    // 只在上方空间不够时才放到下方
    if (popupY < 10) {
      popupY = poolScreenY + arrowSize + margin;
      isBelow = true;
    }

    // 箭头始终指向池子中心，相对于弹窗的位置
    const arrowLeft = popupWidth / 2; // 箭头在弹窗中央

    popupPosition.value = {
      x: 0,
      y: 0,
      arrowLeft: arrowLeft,
      isBelow: isBelow,
    };
  }

  showPoolInfo.value = true;
}

// 将3D世界坐标转换为屏幕坐标
function worldToScreen(worldPosition: THREE.Vector3) {
  const vector = worldPosition.clone();
  vector.project(camera);

  const rect = container.value?.getBoundingClientRect();
  if (!rect) return { x: 0, y: 0 };

  const x = (vector.x * 0.5 + 0.5) * rect.width;
  const y = (vector.y * -0.5 + 0.5) * rect.height;

  return { x, y };
}

// 关闭弹窗
function closePopup() {
  showPoolInfo.value = false;

  // 取消高亮
  if (selectedPool) {
    resetPoolHighlight(selectedPool);
    selectedPool = null;
  }
}

// 开始自动轮播
function startAutoRotation() {
  if (autoRotationTimer || poolObjects.length === 0) return;

  isAutoRotating.value = true;
  guiParams.rotation.enabled = true;
  currentRotationIndex = 0;

  autoRotationTimer = setInterval(() => {
    if (poolObjects.length > 0) {
      const pool = poolObjects[currentRotationIndex];
      selectPool(pool);

      currentRotationIndex = (currentRotationIndex + 1) % poolObjects.length;
    }
  }, guiParams.rotation.speed); // 使用GUI参数中的速度
}

// 停止自动轮播
function stopAutoRotation() {
  if (autoRotationTimer) {
    clearInterval(autoRotationTimer);
    autoRotationTimer = null;
    isAutoRotating.value = false;
    guiParams.rotation.enabled = false;
  }
}

// 重启自动轮播（用于手动点击后的恢复）
function restartAutoRotation() {
  stopAutoRotation();
  setTimeout(() => {
    startAutoRotation();
  }, 5000); // 5秒后重新开始轮播
}

// 高亮养鱼池
function highlightPool(pool: THREE.Object3D) {
  const poolNumber = getPoolNumber(pool);
  const poolData = getPoolData(poolNumber);
  const isAlarm = poolData.isAlarm;

  pool.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      // 保存原始材质
      if (!originalMaterials.has(child)) {
        originalMaterials.set(child, child.material);
      }

      // 根据报警状态创建不同颜色的高亮材质
      const highlightMaterial = new THREE.MeshBasicMaterial({
        color: isAlarm ? 0xff0000 : 0x00ff00, // 报警时红色，正常时绿色
        transparent: true,
        opacity: 0.7,
      });

      child.material = highlightMaterial;
    }
  });
}

// 重置养鱼池高亮
function resetPoolHighlight(pool: THREE.Object3D) {
  const poolBlueColor = 0x4a90e2; // 水池蓝色

  pool.traverse((child) => {
    if (child instanceof THREE.Mesh && originalMaterials.has(child)) {
      // 恢复为蓝色材质而不是原始材质
      const originalMaterial = originalMaterials.get(child) as THREE.Material;

      // 创建蓝色材质
      if (Array.isArray(originalMaterial)) {
        const blueMaterials = originalMaterial.map((mat) => {
          if (
            mat instanceof THREE.MeshStandardMaterial ||
            mat instanceof THREE.MeshLambertMaterial
          ) {
            const blueMat = mat.clone();
            blueMat.color.setHex(poolBlueColor);
            if (mat.transparent) {
              blueMat.transparent = true;
              blueMat.opacity = Math.max(mat.opacity, 0.8);
            }
            return blueMat;
          } else {
            return new THREE.MeshLambertMaterial({
              color: poolBlueColor,
              transparent: mat.transparent,
              opacity: mat.transparent ? Math.max(mat.opacity || 1, 0.8) : 1,
            });
          }
        });
        child.material = blueMaterials;
      } else {
        if (
          originalMaterial instanceof THREE.MeshStandardMaterial ||
          originalMaterial instanceof THREE.MeshLambertMaterial
        ) {
          const blueMaterial = originalMaterial.clone();
          blueMaterial.color.setHex(poolBlueColor);
          if (originalMaterial.transparent) {
            blueMaterial.transparent = true;
            blueMaterial.opacity = Math.max(originalMaterial.opacity, 0.8);
          }
          child.material = blueMaterial;
        } else {
          child.material = new THREE.MeshLambertMaterial({
            color: poolBlueColor,
            transparent: originalMaterial.transparent,
            opacity: originalMaterial.transparent
              ? Math.max(originalMaterial.opacity || 1, 0.8)
              : 1,
          });
        }
      }
    }
  });
}

// 设置池子为水池蓝色
function setPoolsBlueColor() {
  // 定义水池蓝色
  const poolBlueColor = 0x4a90e2; // 水池蓝色

  console.log(`开始为 ${poolObjects.length} 个已识别的养鱼池设置蓝色`);

  // 直接使用已经识别出的池子对象
  poolObjects.forEach((poolObj, index) => {
    console.log(`处理第 ${index + 1} 个池子: ${poolObj.name}`);

    // 遍历池子对象的所有子网格
    poolObj.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        // 保存原始材质（如果还没有保存的话）
        if (!originalMaterials.has(child)) {
          originalMaterials.set(child, child.material);
        }

        // 创建新的蓝色材质
        if (Array.isArray(child.material)) {
          // 如果是材质数组，为每个材质创建蓝色版本
          const newMaterials = child.material.map((mat) => {
            if (
              mat instanceof THREE.MeshStandardMaterial ||
              mat instanceof THREE.MeshLambertMaterial
            ) {
              const newMat = mat.clone();
              newMat.color.setHex(poolBlueColor);
              // 如果有透明度，保持透明效果
              if (mat.transparent) {
                newMat.transparent = true;
                newMat.opacity = Math.max(mat.opacity, 0.8);
              }
              return newMat;
            } else {
              // 对于其他类型的材质，创建基础蓝色材质
              return new THREE.MeshLambertMaterial({
                color: poolBlueColor,
                transparent: mat.transparent,
                opacity: mat.transparent ? Math.max(mat.opacity || 1, 0.8) : 1,
              });
            }
          });
          child.material = newMaterials;
        } else {
          // 单个材质
          if (
            child.material instanceof THREE.MeshStandardMaterial ||
            child.material instanceof THREE.MeshLambertMaterial
          ) {
            const newMaterial = child.material.clone();
            newMaterial.color.setHex(poolBlueColor);
            // 如果有透明度，保持透明效果
            if (child.material.transparent) {
              newMaterial.transparent = true;
              newMaterial.opacity = Math.max(child.material.opacity, 0.8);
            }
            child.material = newMaterial;
          } else {
            // 对于其他类型的材质，创建基础蓝色材质
            child.material = new THREE.MeshLambertMaterial({
              color: poolBlueColor,
              transparent: child.material.transparent,
              opacity: child.material.transparent
                ? Math.max(child.material.opacity || 1, 0.8)
                : 1,
            });
          }
        }

        console.log(
          `已将池子 ${poolObj.name} 的网格 ${
            child.name || "未命名网格"
          } 设置为蓝色`
        );
      }
    });
  });

  console.log(`池子蓝色设置完成，共处理了 ${poolObjects.length} 个池子`);
}

// 恢复所有池子的原始颜色
function resetAllColors(object: THREE.Object3D) {
  object.traverse((child) => {
    if (child instanceof THREE.Mesh && originalMaterials.has(child)) {
      // 恢复原始材质
      child.material = originalMaterials.get(child) as THREE.Material;
      console.log(`已恢复 ${child.name || "未命名网格"} 的原始颜色`);
    }
  });
  console.log("所有颜色已恢复为原始状态");
}

// 调试模型对象名称
function debugModelObjects(object: THREE.Object3D) {
  console.log("=== 模型对象结构调试 ===");

  function traverse(obj: THREE.Object3D, depth = 0) {
    const indent = "  ".repeat(depth);
    const objType = obj.constructor.name;
    const objName = obj.name || "未命名";

    console.log(`${indent}${objType}: "${objName}"`);

    if (obj instanceof THREE.Mesh) {
      console.log(
        `${indent}  - 网格材质类型: ${obj.material.constructor.name}`
      );
      if (Array.isArray(obj.material)) {
        obj.material.forEach((mat, index) => {
          console.log(`${indent}    [${index}] ${mat.constructor.name}`);
        });
      }
    }

    // 递归遍历子对象
    obj.children.forEach((child) => traverse(child, depth + 1));
  }

  traverse(object);
  console.log("=== 调试完成 ===");
}

// 清除手动选择
function clearManualSelection() {
  manualSelectedObjects.forEach((mesh) => {
    if (originalMaterials.has(mesh)) {
      mesh.material = originalMaterials.get(mesh) as THREE.Material;
    }
  });
  manualSelectedObjects.clear();
  console.log("已清除所有手动选择");
}

// 手动选择对象变蓝
function setManualSelectedBlue() {
  const poolBlueColor = 0x4a90e2;

  manualSelectedObjects.forEach((mesh) => {
    // 保存原始材质
    if (!originalMaterials.has(mesh)) {
      originalMaterials.set(mesh, mesh.material);
    }

    // 创建蓝色材质
    if (Array.isArray(mesh.material)) {
      const newMaterials = mesh.material.map((mat) => {
        if (
          mat instanceof THREE.MeshStandardMaterial ||
          mat instanceof THREE.MeshLambertMaterial
        ) {
          const newMat = mat.clone();
          newMat.color.setHex(poolBlueColor);
          return newMat;
        } else {
          return new THREE.MeshLambertMaterial({ color: poolBlueColor });
        }
      });
      mesh.material = newMaterials;
    } else {
      if (
        mesh.material instanceof THREE.MeshStandardMaterial ||
        mesh.material instanceof THREE.MeshLambertMaterial
      ) {
        const newMaterial = mesh.material.clone();
        newMaterial.color.setHex(poolBlueColor);
        mesh.material = newMaterial;
      } else {
        mesh.material = new THREE.MeshLambertMaterial({ color: poolBlueColor });
      }
    }
  });

  console.log(`已将 ${manualSelectedObjects.size} 个手动选择的对象设置为蓝色`);
}

// 遍历场景中的所有对象，查找养鱼池
function findAndPrintPools(object: THREE.Object3D) {
  const pools: Array<{ name: string; position: THREE.Vector3; userData: any }> =
    [];

  let poolCounter = 1; // 池子计数器

  // 递归遍历所有子对象
  function traverse(obj: THREE.Object3D) {
    // 检查对象名称是否包含"养鱼池"
    if (obj.name && obj.name.includes("养鱼池")) {
      pools.push({
        name: obj.name,
        position: obj.position.clone(),
        userData: obj.userData,
      });

      // 为这个池子对象添加一个唯一的编号
      obj.userData.poolNumber = poolCounter;
      poolCounter++;

      // 将养鱼池对象添加到可点击对象数组中
      poolObjects.push(obj);

      console.log(
        `发现养鱼池: ${obj.name}, 分配编号: ${obj.userData.poolNumber}`
      );
    }

    // 递归遍历子对象
    obj.children.forEach((child) => traverse(child));
  }

  traverse(object);

  console.log(`总共找到 ${pools.length} 个养鱼池`);

  return pools;
}

// 加载GLB模型
function loadPoolModel() {
  const loader = new GLTFLoader();

  // 使用指定路径
  const modelPath = "/src/assets/pool.glb";
  console.log(`Loading model from: ${modelPath}`);

  loader.load(
    modelPath,
    (gltf) => {
      poolModel = gltf.scene;

      // 调整模型大小和位置 - 使用GUI参数
      gltf.scene.scale.setScalar(guiParams.model.scale);
      gltf.scene.position.set(
        guiParams.model.x,
        guiParams.model.y,
        guiParams.model.z
      );
      gltf.scene.rotation.x = guiParams.model.rotationX;
      gltf.scene.rotation.y = guiParams.model.rotationY;
      gltf.scene.rotation.z = guiParams.model.rotationZ;
      console.log("🚀 ~ loadPoolModel ~  gltf.scene:", gltf.scene);

      // 添加到场景
      scene.add(gltf.scene);

      // 遍历场景中的所有对象，查找养鱼池
      findAndPrintPools(gltf.scene);

      // 设置池子为水池蓝色
      setPoolsBlueColor();

      // 模型加载完成后启动自动轮播
      setTimeout(() => {
        startAutoRotation();
      }, 2000); // 延迟2秒启动，确保模型完全加载

      console.log(`Model loaded successfully from: ${modelPath}`);
    },
    (progress) => {
      console.log(
        `Loading progress:`,
        (progress.loaded / progress.total) * 100 + "%"
      );
    },
    (error) => {
      console.error(`Error loading model:`, error);
      // 创建一个简单的替代几何体
      createFallbackGeometry();
    }
  );
}

// 创建备用几何体（如果FBX加载失败）
function createFallbackGeometry() {
  console.log("Creating fallback geometry...");

  // 创建一个简单的池子形状
  const poolGeometry = new THREE.CylinderGeometry(2, 2, 0.5, 32);
  const poolMaterial = new THREE.MeshLambertMaterial({
    color: 0x4a90e2,
    transparent: true,
    opacity: 0.8,
  });

  const pool = new THREE.Mesh(poolGeometry, poolMaterial);
  pool.position.set(0, 0, 0);

  poolModel = new THREE.Group();
  poolModel.add(pool);

  scene.add(poolModel);

  console.log("Fallback pool geometry created");
}

// 渲染函数
function animate() {
  animationId = requestAnimationFrame(animate);

  // 更新轨道控制器
  controls.update();

  // 渲染
  renderer.render(scene, camera);
}

// 处理窗口大小变化
function handleResize() {
  if (!container.value || !camera || !renderer) return;

  const width = container.value.clientWidth;
  const height = container.value.clientHeight;

  camera.aspect = width / height;
  camera.updateProjectionMatrix();
  renderer.setSize(width, height);
}

// 清理资源
function cleanup() {
  // 停止自动轮播
  stopAutoRotation();

  if (animationId) {
    cancelAnimationFrame(animationId);
  }

  // 清理GUI
  if (gui) {
    gui.destroy();
    gui = null;
  }

  // 清理控制器
  if (controls) {
    controls.dispose();
  }

  // 清理模型
  if (poolModel) {
    scene.remove(poolModel);
    poolModel = null;
  }

  // 清理事件监听器
  if (renderer && renderer.domElement) {
    renderer.domElement.removeEventListener("click", onMouseClick, false);
  }

  if (renderer) {
    renderer.dispose();
  }

  // 清理数组和映射
  poolObjects.length = 0;
  originalMaterials.clear();
  selectedPool = null;

  window.removeEventListener("resize", handleResize);
}

// 获取报警数据的函数，供其他组件使用
function getAlarmData() {
  const alarms: Array<{
    poolName: string;
    deviceName: string;
    reason: string;
    time: string;
    level: "high" | "medium" | "low";
  }> = [];

  // 遍历前36个池子，检查报警状态
  for (let i = 1; i <= 36; i++) {
    const poolData = getPoolData(i);
    if (poolData.isAlarm) {
      const dataIndex = Math.min(i - 1, 35);
      const data = poolsData[dataIndex];

      if (data) {
        // 检查各项指标，生成具体的报警信息
        if (!isValueNormal("water_temperature", data.water_temperature)) {
          console.log(123);
          alarms.push({
            poolName: poolData.name,
            deviceName: "温度传感器",
            reason: data.water_temperature > 29 ? "水温过高" : "水温过低",
            time: new Date().toLocaleString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
            }),
            level: "high",
          });
        }

        if (!isValueNormal("PH", data.PH)) {
          alarms.push({
            poolName: poolData.name,
            deviceName: "pH传感器",
            reason: data.PH > 8.8 ? "pH值过高" : "pH值过低",
            time: new Date().toLocaleString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
            }),
            level: "medium",
          });
        }

        if (!isValueNormal("dissolved_oxygen", data.dissolved_oxygen)) {
          alarms.push({
            poolName: poolData.name,
            deviceName: "溶氧仪",
            reason: "溶氧不足",
            time: new Date().toLocaleString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
            }),
            level: "high",
          });
        }

        if (!isValueNormal("water_level", data.water_level)) {
          alarms.push({
            poolName: poolData.name,
            deviceName: "水位传感器",
            reason: data.water_level > 1.45 ? "水位过高" : "水位过低",
            time: new Date().toLocaleString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
            }),
            level: "medium",
          });
        }

        if (!isValueNormal("salinity", data.salinity)) {
          alarms.push({
            poolName: poolData.name,
            deviceName: "盐度计",
            reason: data.salinity > 26 ? "盐度过高" : "盐度过低",
            time: new Date().toLocaleString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
            }),
            level: "low",
          });
        }
      }
    }
  }

  return alarms;
}

// 暴露函数给其他组件使用
defineExpose({
  getAlarmData,
});

// 组件挂载时初始化
onMounted(async () => {
  await loadPoolsData(); // 先加载数据
  initThreeJS();
  window.addEventListener("resize", handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  cleanup();
});
</script>

<style scoped>
.pool-info-popup {
  position: absolute;
  width: 180px;
  background: linear-gradient(
    135deg,
    rgba(0, 20, 40, 0.75) 0%,
    rgba(0, 40, 80, 0.75) 100%
  );
  border: 1px solid rgba(102, 204, 255, 0.4);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2), 0 0 12px rgba(102, 204, 255, 0.15);
  backdrop-filter: blur(8px);
  z-index: 1000;
  animation: popupFadeIn 0.3s ease-out;
  font-family: "Microsoft YaHei", sans-serif;
}

/* 弹窗箭头 */
.popup-arrow {
  position: absolute;
  bottom: -8px;
  left: var(--arrow-left, 50%);
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid rgba(0, 40, 80, 0.75);
  z-index: 1001;
}

.popup-arrow::before {
  content: "";
  position: absolute;
  top: -9px;
  left: -8px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid rgba(102, 204, 255, 0.4);
}

/* 弹窗在池子下方时的箭头样式 */
.popup-below .popup-arrow {
  bottom: auto;
  top: -8px;
  border-top: none;
  border-bottom: 8px solid rgba(0, 40, 80, 0.75);
}

.popup-below .popup-arrow::before {
  top: auto;
  bottom: -9px;
  border-top: none;
  border-bottom: 8px solid rgba(102, 204, 255, 0.4);
}

@keyframes popupFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid rgba(102, 204, 255, 0.2);
  background: linear-gradient(
    90deg,
    rgba(102, 204, 255, 0.08) 0%,
    transparent 100%
  );
}

.popup-title {
  margin: 0;
  color: #66ccff;
  font-size: 12px;
  font-weight: 600;
  text-shadow: 0 0 8px rgba(102, 204, 255, 0.4);
}

.close-btn {
  background: none;
  border: none;
  color: #66ccff;
  font-size: 16px;
  cursor: pointer;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(102, 204, 255, 0.2);
  transform: scale(1.1);
}

.popup-content {
  padding: 10px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.info-item {
  background: rgba(102, 204, 255, 0.03);
  border: 1px solid rgba(102, 204, 255, 0.15);
  border-radius: 4px;
  padding: 6px;
  text-align: center;
  transition: all 0.3s ease;
}

.info-item:hover {
  background: rgba(102, 204, 255, 0.08);
  border-color: rgba(102, 204, 255, 0.3);
  transform: translateY(-1px);
}

.info-label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 10px;
  margin-bottom: 2px;
  font-weight: 500;
}

.info-value {
  color: #66ccff;
  font-size: 12px;
  font-weight: 600;
  text-shadow: 0 0 6px rgba(102, 204, 255, 0.3);
}

/* 特殊的第5个项目占满整行 */
.info-item:nth-child(5) {
  grid-column: 1 / -1;
}

/* 报警状态样式 */
.alarm-popup {
  border-color: rgba(255, 0, 0, 0.5) !important;
  box-shadow: 0 4px 16px rgba(255, 0, 0, 0.2), 0 0 12px rgba(255, 0, 0, 0.25) !important;
  background: linear-gradient(
    135deg,
    rgba(40, 0, 0, 0.75) 0%,
    rgba(80, 0, 0, 0.75) 100%
  ) !important;
}

.alarm-popup .popup-header {
  background: linear-gradient(
    90deg,
    rgba(255, 0, 0, 0.12) 0%,
    transparent 100%
  ) !important;
  border-bottom-color: rgba(255, 0, 0, 0.3) !important;
}

.alarm-popup .popup-title {
  color: #ff6666 !important;
  text-shadow: 0 0 8px rgba(255, 0, 0, 0.4) !important;
}

.alarm-badge {
  background: #ff0000;
  color: white;
  padding: 1px 6px;
  border-radius: 8px;
  font-size: 10px;
  margin-left: 6px;
  animation: alarmBlink 1s infinite;
}

@keyframes alarmBlink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0.5;
  }
}

.alarm-popup .info-item {
  border-color: rgba(255, 0, 0, 0.2) !important;
  background: rgba(255, 0, 0, 0.03) !important;
}

.alarm-popup .info-value {
  color: #ff6666 !important;
  text-shadow: 0 0 6px rgba(255, 0, 0, 0.3) !important;
}

/* 报警状态箭头样式 */
.alarm-arrow {
  border-top-color: rgba(80, 0, 0, 0.75) !important;
}

.alarm-arrow::before {
  border-top-color: rgba(255, 0, 0, 0.5) !important;
}

/* 报警状态弹窗在下方时的箭头样式 */
.popup-below .alarm-arrow {
  border-bottom-color: rgba(80, 0, 0, 0.75) !important;
}

.popup-below .alarm-arrow::before {
  border-bottom-color: rgba(255, 0, 0, 0.5) !important;
}
</style>
